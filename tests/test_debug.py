"""Tests for debug mode functionality."""

import pytest
from dataclasses import dataclass

from parmancer import (
    Pa<PERSON><PERSON><PERSON><PERSON>,
    <PERSON>rse<PERSON>,
    DebugTextState,
    gather,
    regex,
    string,
    take,
    seq,
    one_of,
)


def test_debug_mode_basic_failure():
    """Test that debug mode provides enhanced error information on failure."""
    parser = string("hello")
    
    # Test normal mode
    with pytest.raises(ParseError) as exc_info:
        parser.parse("world")
    
    normal_error = str(exc_info.value)
    assert "Debug information:" not in normal_error
    
    # Test debug mode
    with pytest.raises(ParseError) as exc_info:
        parser.parse("world", debug=True)
    
    debug_error = str(exc_info.value)
    assert "Debug information:" in debug_error
    assert "Parse tree:" in debug_error
    assert "Parser path:" in debug_error
    assert "Context:" in debug_error


def test_debug_mode_with_complex_parser():
    """Test debug mode with a more complex parser structure."""
    
    @dataclass
    class Person:
        name: str = take(regex(r"\w+"))
        age: int = take(string(" ") >> regex(r"\d+").map(int))
    
    parser = gather(Person)
    
    # This should fail when trying to parse the age
    with pytest.raises(ParseError) as exc_info:
        parser.parse("John abc", debug=True)
    
    debug_error = str(exc_info.value)
    assert "Debug information:" in debug_error
    assert "Parse tree:" in debug_error
    # Should show the parser path that led to the failure
    assert "Parser path:" in debug_error


def test_debug_mode_success_case():
    """Test that debug mode works correctly for successful parsing."""
    parser = string("hello")
    
    # Should work the same in both modes for successful parsing
    result_normal = parser.parse("hello")
    result_debug = parser.parse("hello", debug=True)
    
    assert result_normal == result_debug == "hello"


def test_debug_text_state_directly():
    """Test DebugTextState functionality directly."""
    state = DebugTextState.start("hello world")
    
    # Test that it behaves like a normal TextState
    assert state.text == "hello world"
    assert state.index == 0
    assert state.failures == tuple()
    
    # Test that it has the tree attribute
    assert hasattr(state, 'tree')
    assert state.tree.name == "Parser"
    assert state.tree.children == []


def test_debug_state_progress():
    """Test that DebugTextState maintains tree state across progress calls."""
    state = DebugTextState.start("hello")
    
    # Progress to a new position
    new_state = state.progress(3)
    
    # Should maintain the tree
    assert hasattr(new_state, 'tree')
    assert isinstance(new_state, DebugTextState)
    assert new_state.index == 3


def test_debug_mode_with_match_method():
    """Test that debug mode works with the match method as well."""
    parser = string("hello")
    
    # Test normal match
    result_normal = parser.match("world")
    assert not result_normal.status
    
    # Test debug match
    result_debug = parser.match("world", debug=True)
    assert not result_debug.status
    assert isinstance(result_debug.state, DebugTextState)


def test_debug_info_format():
    """Test that debug information is properly formatted."""
    parser = string("expected") >> string("text")

    with pytest.raises(ParseError) as exc_info:
        parser.parse("wrong input", debug=True)

    debug_error = str(exc_info.value)

    # Check that all expected sections are present
    assert "Debug information:" in debug_error
    assert "=" * 80 in debug_error  # Header separator
    assert "Parse tree:" in debug_error
    assert "-" * 80 in debug_error  # Section separators
    assert "Furthest parsing position" in debug_error
    assert "Context at furthest position:" in debug_error


def test_debug_mode_preserves_original_error():
    """Test that debug mode includes the original error information."""
    parser = string("hello")
    
    with pytest.raises(ParseError) as exc_info:
        parser.parse("world", debug=True)
    
    error_str = str(exc_info.value)
    
    # Should contain both the original error and debug info
    assert "failed with" in error_str  # Original error format
    assert "Debug information:" in error_str  # Debug info


def test_debug_mode_with_nested_parsers():
    """Test debug mode with nested parser structures."""
    inner_parser = string("inner")
    outer_parser = string("outer") >> inner_parser

    with pytest.raises(ParseError) as exc_info:
        outer_parser.parse("outer wrong", debug=True)

    debug_error = str(exc_info.value)
    assert "Debug information:" in debug_error
    # The parse tree should show the nested structure
    assert "Parse tree:" in debug_error


def test_furthest_parser_tracking():
    """Test that debug mode correctly identifies the furthest parser that attempted to parse."""
    # Create a parser with multiple alternatives that fail at different positions
    parser = one_of(
        seq(string('hello'), string(' '), regex(r'\d+')),
        seq(string('hello'), string(' '), regex(r'[A-Z]+'))
    )

    with pytest.raises(ParseError) as exc_info:
        parser.parse("hello world", debug=True)

    debug_error = str(exc_info.value)

    # Should show information about the furthest parsing position
    assert "Furthest parsing position (index 6):" in debug_error
    # Should show both failed parsers at the furthest position
    assert "Failed parser: \\d+" in debug_error
    assert "Failed parser: [A-Z]+" in debug_error
    # Should show all failures section since there are multiple
    assert "All parsing failures:" in debug_error


def test_debug_mode_shows_successful_parsers():
    """Test that debug mode shows which parsers succeeded before the failure."""
    parser = seq(string('hello'), string(' '), regex(r'\d+'))

    with pytest.raises(ParseError) as exc_info:
        parser.parse("hello world", debug=True)

    debug_error = str(exc_info.value)

    # Should show the successful parsers in the parse tree
    assert "'hello': 'hello'" in debug_error
    assert "' ': ' '" in debug_error
    # Should show the failed parser
    assert "<<<Parser failed>>>" in debug_error
