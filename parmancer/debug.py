"""
Debug mode functionality for parmancer parsers.

This module provides enhanced error reporting for parser failures by capturing
and displaying detailed information about the parser state, including:
- The parse tree showing which parsers have run and their results
- The current parser path (stack trace of parser names)
- Context around the current parsing position
- Detailed failure information

This is similar to debug mode in compiled languages like Rust, where additional
information is kept at the cost of performance.
"""

from __future__ import annotations

import inspect
import operator
from dataclasses import dataclass, field
from functools import reduce
from typing import Any, List, Tuple

from typing_extensions import Self, TypeVar

from parmancer.parser import TextState, Result, FailureInfo

_T = TypeVar("_T")


class _Missing:
    pass


Missing = _Missing()


@dataclass
class Node:
    """Represent a node of the parse tree: a parser name, its children, and a result if one has been parsed."""

    name: str
    children: List[Node]
    result: Any = Missing

    def become(self, other: Node) -> None:
        self.result = other.result

    @staticmethod
    def default() -> Node:
        return Node("Parser", [])


def append_tree(
    tree: Node,
    path: Tuple[str, ...],
    leaf: Node,
    prune_children_of_results: bool = False,
) -> None:
    """
    Match the path down the right-hand elements of the tree, either finding existing
    nodes or creating a new branch from the point where the nodes no longer overlap
    """
    node = tree
    parent = node
    assert len(path) > 0, "Tree append logic depends on path depth > 0"
    for part in path:
        child = node.children[-1] if node.children else None
        if child is None or child.name != part or child.result is not Missing:
            # Have to create the child node
            child = Node(part, [])
            node.children.append(child)
        parent = node
        node = child
    if parent.name.startswith("field:"):
        # Hack to make dataclass fields get their result
        parent.result = leaf.result
    node.become(leaf)

    if prune_children_of_results:
        node.children = []


def display_tree_order_by_return(tree: Node, indent_width: int = 2) -> str:
    """
    Display a tree, where the order top-to-bottom is determined by
    when the parser returned a result (most recent at bottom).
    Returns the tree as a string instead of printing it.
    """
    hbar = "─" * indent_width
    vbar = "│" + " " * indent_width

    space = " " * (indent_width + 1)
    bi_split = "├" + hbar
    top_split = "┌" + hbar
    bottom_split = "└" + hbar

    lines = []

    def _display(
        tree: Node,
        indent: str = "",
        gutter: str = space,
        split: str = bottom_split,
        depth: int = 0,
    ) -> None:
        display_before = tree.result is Missing
        if display_before:
            # Top-down print
            lines.append(indent + split + tree.name)
            if len(tree.children) == 0:
                return
            for child in tree.children[:-1]:
                _display(child, indent + gutter, vbar, bi_split, depth + 1)
            child = tree.children[-1]

            _display(child, indent + gutter, space, bottom_split, depth + 1)
        else:
            # Bottom-up print
            if len(tree.children) == 0:
                lines.append(indent + split + f"{tree.name}: {repr(tree.result)}")
                return
            child = tree.children[0]

            _display(
                child,
                indent + (space if split == top_split else vbar),
                vbar,
                top_split,
                depth + 1,
            )
            for child in tree.children[1:]:
                _display(
                    child,
                    indent + (space if split == top_split else vbar),
                    vbar,
                    bi_split,
                    depth + 1,
                )
            lines.append(indent + split + f"{tree.name}: {repr(tree.result)}")

    _display(tree)
    return "\n".join(lines)


@dataclass
class ParseStack:
    path: Tuple[str, ...]

    @staticmethod
    def get_from_stack() -> ParseStack:
        stack = inspect.stack()
        try:
            parse_result_frames = [
                element.frame.f_locals["self"].name.split("/")
                for element in stack
                if element.function == "parse_result" and "self" in element.frame.f_locals
                and hasattr(element.frame.f_locals["self"], "name")
            ]

            if parse_result_frames:
                context = reduce(operator.add, list(reversed(parse_result_frames)))
                return ParseStack(context)
            else:
                # No parse_result frames found, return empty path
                return ParseStack(tuple())
        finally:
            del stack

    def __str__(self: Self) -> str:
        return "/".join(self.path)


def format_debug_info(state: TextState, tree: Node) -> str:
    """
    Format debug information for display when a parser fails.
    Returns a formatted string containing the parse tree, parser path,
    context, and failures, focusing on the furthest parsers that attempted to parse.
    """
    lines = []
    lines.append("Debug information:")
    lines.append("=" * 80)
    lines.append("Parse tree:")
    lines.append(display_tree_order_by_return(tree))
    lines.append("-" * 80)

    # Show information about the furthest failures
    if state.failures:
        furthest_index = max(failure.index for failure in state.failures)
        furthest_failures = [f for f in state.failures if f.index == furthest_index]

        lines.append(f"Furthest parsing position (index {furthest_index}):")
        for failure in furthest_failures:
            lines.append(f"  Failed parser: {failure.message}")
    else:
        lines.append("No specific failure information available")

    lines.append("-" * 80)
    lines.append("Context at furthest position:")
    if state.failures:
        furthest_state = state.at(max(failure.index for failure in state.failures))
        lines.append(furthest_state.context_display().rstrip())
    else:
        lines.append(state.context_display().rstrip())
    lines.append("-" * 80)

    # Show all failures for completeness
    if state.failures and len(state.failures) > 1:
        lines.append("All parsing failures:")
        for failure in sorted(state.failures, key=lambda f: f.index, reverse=True):
            lines.append(f"  Index {failure.index}: {failure.message}")
        lines.append("-" * 80)

    lines.append("=" * 80)

    return "\n".join(lines)


@dataclass(frozen=True)
class DebugTextState(TextState):
    """
    A TextState subclass that captures parser execution information for debug display.
    When a parser fails, this state can provide detailed information about what
    parsers were attempted and where the failure occurred.
    """
    tree: Node = field(default_factory=Node.default)

    def progress(
        self: Self, index: int, failures: Tuple[FailureInfo, ...] = tuple()
    ) -> Self:
        """
        Override progress to maintain the tree state across state transitions.
        """
        # Use the parent's progress method but ensure we keep our tree
        new_state = super().progress(index, failures)
        # The tree should already be preserved by the parent's progress method
        # since it uses **{field.name: getattr(self, field.name) for field in fields(self)}
        return new_state

    def success(self: Self, value: _T) -> Result[_T]:
        # Capture successful parser results in the tree
        call_stack = inspect.stack()
        try:
            stack = ParseStack.get_from_stack()
            if stack.path:  # Only add to tree if we have a valid path
                node = Node(stack.path[-1], [], result=value)
                append_tree(self.tree, stack.path, node)
        except:
            # If we can't get stack info, just continue without adding to tree
            pass
        finally:
            del call_stack

        return super().success(value)

    def failure(self: Self, message: str) -> Result[Any]:
        # Capture failure in the tree when it actually occurs
        call_stack = inspect.stack()
        try:
            stack = ParseStack.get_from_stack()
            if stack.path:  # Only add to tree if we have a valid path
                node = Node(stack.path[-1], [], result="<<<Parser failed>>>")
                append_tree(self.tree, stack.path, node)
        except:
            # If we can't get stack info, just continue without adding to tree
            pass
        finally:
            del call_stack

        return super().failure(message)

    def get_debug_info(self: Self) -> str:
        """Get formatted debug information for this state."""
        return format_debug_info(self, self.tree)
